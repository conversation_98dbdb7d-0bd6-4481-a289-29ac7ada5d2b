const alipayConfig = require('../config/alipayConfig');
const orderService = require('./orderService');
const logger = require('../log/logger');

/**
 * 支付宝支付服务
 */
class AlipayService {
    /**
     * 创建支付订单并生成二维码
     */
    async createPayment(paymentData) {
        let orderResult;
        try {
            // 1. 创建订单
            orderResult = await orderService.createOrder({
                userId: paymentData.userId,
                productType: paymentData.productType || 'single_service',
                productId: paymentData.productId,
                productName: paymentData.productName || '服务购买',
                totalAmount: paymentData.amount,
                paymentMethod: 'alipay'
            });

            // 2. 调用支付宝接口生成支付二维码
            const alipaySdk = alipayConfig.getSdk();
            
            const bizContent = {
                out_trade_no: orderResult.outTradeNo,
                total_amount: paymentData.amount.toString(),
                subject: paymentData.productName || '服务购买',
                product_code: 'FAST_INSTANT_TRADE_PAY',
                qr_pay_mode: '1', // 订单码-简约前置模式
                qrcode_width: 200,
                time_expire: this.formatExpireTime(orderResult.expireTime)
            };

            // 记录请求日志
            await orderService.logPaymentAction(
                orderResult.orderId,
                'create',
                { bizContent },
                null,
                'success'
            );

            // 临时使用模拟的二维码进行测试
            // TODO: 在生产环境中需要使用真实的支付宝接口
            const isDevelopment = process.env.NODE_ENV !== 'production';

            if (isDevelopment) {
                // 开发环境：生成模拟二维码
                const mockQrCode = `https://qr.alipay.com/mock_${orderResult.outTradeNo}`;

                // 更新订单二维码URL
                await orderService.updateOrderStatus(orderResult.orderId, 'pending', {
                    qrCodeUrl: mockQrCode
                });

                // 记录响应日志
                await orderService.logPaymentAction(
                    orderResult.orderId,
                    'create',
                    { bizContent },
                    { qrCodeUrl: mockQrCode },
                    'success'
                );

                logger.info(`支付订单创建成功(开发模式): ${orderResult.orderId}`);

                return {
                    orderId: orderResult.orderId,
                    outTradeNo: orderResult.outTradeNo,
                    qrCodeUrl: mockQrCode,
                    expireTime: orderResult.expireTime,
                    amount: paymentData.amount
                };
            } else {
                // 生产环境：调用真实的支付宝接口
                const result = await alipaySdk.exec('alipay.trade.precreate', {
                    bizContent: {
                        out_trade_no: orderResult.outTradeNo,
                        total_amount: paymentData.amount.toString(),
                        subject: paymentData.productName || '服务购买',
                        time_expire: this.formatExpireTime(orderResult.expireTime)
                    }
                });

                // 检查支付宝接口返回结果
                if (result.code === '10000' && result.qr_code) {
                    // 更新订单二维码URL
                    await orderService.updateOrderStatus(orderResult.orderId, 'pending', {
                        qrCodeUrl: result.qr_code
                    });

                    // 记录响应日志
                    await orderService.logPaymentAction(
                        orderResult.orderId,
                        'create',
                        { bizContent },
                        { qrCodeUrl: result.qr_code },
                        'success'
                    );

                    logger.info(`支付订单创建成功: ${orderResult.orderId}`);

                    return {
                        orderId: orderResult.orderId,
                        outTradeNo: orderResult.outTradeNo,
                        qrCodeUrl: result.qr_code,
                        expireTime: orderResult.expireTime,
                        amount: paymentData.amount
                    };
                } else {
                    throw new Error(`支付宝接口调用失败: ${result.msg || result.sub_msg || '未知错误'}`);
                }
            }

        } catch (error) {
            logger.error('创建支付订单失败:', error);

            // 记录错误日志
            if (typeof orderResult !== 'undefined' && orderResult && orderResult.orderId) {
                await orderService.logPaymentAction(
                    orderResult.orderId,
                    'create',
                    paymentData,
                    null,
                    'failed',
                    error.message
                );
            }

            throw error;
        }
    }

    /**
     * 查询支付状态
     */
    async queryPayment(orderId) {
        try {
            // 1. 查询本地订单
            const order = await orderService.getOrder(orderId);
            if (!order) {
                throw new Error('订单不存在');
            }

            // 如果订单已经是已支付状态，直接返回
            if (order.payment_status === 'paid') {
                return {
                    orderId: order.order_id,
                    status: 'paid',
                    tradeNo: order.trade_no,
                    paidTime: order.paid_time
                };
            }

            // 2. 查询支付状态
            const isDevelopment = process.env.NODE_ENV !== 'production';

            if (isDevelopment) {
                // 开发环境：模拟支付查询
                // 如果订单创建时间超过30秒，模拟支付成功
                const orderCreateTime = new Date(order.created_at);
                const now = new Date();
                const timeDiff = (now - orderCreateTime) / 1000; // 秒

                if (timeDiff > 30) {
                    // 模拟支付成功
                    const mockTradeNo = `mock_trade_${Date.now()}`;
                    await orderService.updateOrderStatus(orderId, 'paid', {
                        tradeNo: mockTradeNo
                    });

                    // 记录查询日志
                    await orderService.logPaymentAction(
                        orderId,
                        'query',
                        { orderId },
                        { status: 'paid', tradeNo: mockTradeNo },
                        'success'
                    );

                    return {
                        orderId: orderId,
                        status: 'paid',
                        tradeNo: mockTradeNo,
                        paidTime: new Date()
                    };
                } else {
                    // 等待支付
                    return {
                        orderId: orderId,
                        status: 'pending'
                    };
                }
            } else {
                // 生产环境：调用真实的支付宝查询接口
                const alipaySdk = alipayConfig.getSdk();

                const bizContent = {
                    out_trade_no: order.out_trade_no
                };

                const result = await alipaySdk.exec('alipay.trade.query', {
                    bizContent
                });

                // 记录查询日志
                await orderService.logPaymentAction(
                    orderId,
                    'query',
                    { bizContent },
                    result,
                    'success'
                );

                // 3. 处理查询结果
                if (result.code === '10000') {
                    const tradeStatus = result.trade_status;

                    if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
                        // 支付成功，更新订单状态
                        await orderService.updateOrderStatus(orderId, 'paid', {
                            tradeNo: result.trade_no
                        });

                        return {
                            orderId: orderId,
                            status: 'paid',
                            tradeNo: result.trade_no,
                            paidTime: new Date()
                        };
                    } else if (tradeStatus === 'TRADE_CLOSED') {
                        // 交易关闭
                        await orderService.updateOrderStatus(orderId, 'closed');
                        return {
                            orderId: orderId,
                            status: 'closed'
                        };
                    } else {
                        // 等待支付
                        return {
                            orderId: orderId,
                            status: 'pending'
                        };
                    }
                } else {
                    // 查询失败
                    logger.error('支付宝查询失败:', result);
                    return {
                        orderId: orderId,
                        status: 'pending'
                    };
                }
            }

        } catch (error) {
            logger.error('查询支付状态失败:', error);
            
            // 记录错误日志
            await orderService.logPaymentAction(
                orderId,
                'query',
                { orderId },
                null,
                'failed',
                error.message
            );
            
            throw error;
        }
    }

    /**
     * 关闭订单
     */
    async closeOrder(orderId) {
        try {
            // 1. 查询本地订单
            const order = await orderService.getOrder(orderId);
            if (!order) {
                throw new Error('订单不存在');
            }

            if (order.payment_status !== 'pending') {
                throw new Error('只能关闭待支付订单');
            }

            // 2. 调用支付宝关闭接口
            const alipaySdk = alipayConfig.getSdk();
            
            const bizContent = {
                out_trade_no: order.out_trade_no
            };

            const result = await alipaySdk.exec('alipay.trade.close', {
                bizContent
            });

            // 记录关闭日志
            await orderService.logPaymentAction(
                orderId,
                'close',
                { bizContent },
                result,
                'success'
            );

            // 3. 更新本地订单状态
            await orderService.updateOrderStatus(orderId, 'closed');

            logger.info(`订单关闭成功: ${orderId}`);

            return {
                orderId: orderId,
                status: 'closed'
            };

        } catch (error) {
            logger.error('关闭订单失败:', error);
            
            // 记录错误日志
            await orderService.logPaymentAction(
                orderId,
                'close',
                { orderId },
                null,
                'failed',
                error.message
            );
            
            throw error;
        }
    }

    /**
     * 格式化过期时间
     */
    formatExpireTime(expireTime) {
        // 支付宝要求的时间格式：yyyy-MM-dd HH:mm:ss
        return expireTime.toISOString().slice(0, 19).replace('T', ' ');
    }

    /**
     * 处理支付宝异步通知
     */
    async handleNotify(notifyData) {
        try {
            const outTradeNo = notifyData.out_trade_no;
            const tradeStatus = notifyData.trade_status;
            const tradeNo = notifyData.trade_no;

            // 查询订单
            const order = await orderService.getOrderByOutTradeNo(outTradeNo);
            if (!order) {
                logger.error('通知订单不存在:', outTradeNo);
                return false;
            }

            // 记录通知日志
            await orderService.logPaymentAction(
                order.order_id,
                'notify',
                notifyData,
                null,
                'success'
            );

            // 处理支付成功
            if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
                await orderService.updateOrderStatus(order.order_id, 'paid', {
                    tradeNo: tradeNo
                });

                logger.info(`支付成功通知处理完成: ${order.order_id}`);
                return true;
            }

            return true;
        } catch (error) {
            logger.error('处理支付宝通知失败:', error);
            return false;
        }
    }
}

module.exports = new AlipayService();
