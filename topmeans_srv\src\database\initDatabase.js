const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4'
};

/**
 * 初始化数据库表
 */
async function initDatabase() {
    let connection;
    try {
        console.log('开始初始化数据库...');
        
        // 创建数据库连接
        connection = await mysql.createConnection(dbConfig);
        
        // 读取SQL文件
        const sqlFilePath = path.join(__dirname, 'payment_orders.sql');
        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
        
        // 分割SQL语句（按分号分割）
        const sqlStatements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);
        
        // 执行每个SQL语句
        for (const statement of sqlStatements) {
            if (statement.trim()) {
                console.log('执行SQL:', statement.substring(0, 50) + '...');
                await connection.execute(statement);
            }
        }
        
        console.log('数据库初始化完成！');
        
        // 验证表是否创建成功
        const [tables] = await connection.execute(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = ? 
            AND TABLE_NAME IN ('payment_orders', 'payment_logs')
        `, [process.env.DB_NAME]);
        
        console.log('已创建的表:', tables.map(t => t.TABLE_NAME));
        
    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
    initDatabase()
        .then(() => {
            console.log('数据库初始化成功完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('数据库初始化失败:', error);
            process.exit(1);
        });
}

module.exports = { initDatabase };
