export default {
    kSocketId: Symbol('socket id'),
    kSocketStartTime: Symbol('socket start time'),
    kSocketConnectedTime: Symbol('socket connected time'),
    kSocketConnectErrorTime: Symbol('socket connectError time'),
    kSocketRequestEndTime: Symbol('socket request end time'),
    kSocketLocalAddress: Symbol('socket local address'),
    kSocketLocalPort: Symbol('socket local port'),
    kSocketConnectHost: Symbol('socket connect params: host'),
    kSocketConnectPort: Symbol('socket connect params: port'),
    kSocketConnectProtocol: Symbol('socket connect params: protocol'),
    kHandledRequests: Symbol('handled requests per socket'),
    kHandledResponses: Symbol('handled responses per socket'),
    kRequestSocket: Symbol('request on the socket'),
    kRequestId: Symbol('request id'),
    kRequestStartTime: Symbol('request start time'),
    kEnableRequestTiming: Symbol('enable request timing or not'),
    kRequestTiming: Symbol('request timing'),
    kRequestOriginalOpaque: Symbol('request original opaque'),
    kRequestInternalOpaque: Symbol('request internal opaque'),
    kErrorSocket: Symbol('socket of error'),
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic3ltYm9scy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9zeW1ib2xzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGVBQWU7SUFDYixTQUFTLEVBQUUsTUFBTSxDQUFDLFdBQVcsQ0FBQztJQUM5QixnQkFBZ0IsRUFBRSxNQUFNLENBQUMsbUJBQW1CLENBQUM7SUFDN0Msb0JBQW9CLEVBQUUsTUFBTSxDQUFDLHVCQUF1QixDQUFDO0lBQ3JELHVCQUF1QixFQUFFLE1BQU0sQ0FBQywwQkFBMEIsQ0FBQztJQUMzRCxxQkFBcUIsRUFBRSxNQUFNLENBQUMseUJBQXlCLENBQUM7SUFDeEQsbUJBQW1CLEVBQUUsTUFBTSxDQUFDLHNCQUFzQixDQUFDO0lBQ25ELGdCQUFnQixFQUFFLE1BQU0sQ0FBQyxtQkFBbUIsQ0FBQztJQUM3QyxrQkFBa0IsRUFBRSxNQUFNLENBQUMsNkJBQTZCLENBQUM7SUFDekQsa0JBQWtCLEVBQUUsTUFBTSxDQUFDLDZCQUE2QixDQUFDO0lBQ3pELHNCQUFzQixFQUFFLE1BQU0sQ0FBQyxpQ0FBaUMsQ0FBQztJQUNqRSxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsNkJBQTZCLENBQUM7SUFDdkQsaUJBQWlCLEVBQUUsTUFBTSxDQUFDLDhCQUE4QixDQUFDO0lBQ3pELGNBQWMsRUFBRSxNQUFNLENBQUMsdUJBQXVCLENBQUM7SUFDL0MsVUFBVSxFQUFFLE1BQU0sQ0FBQyxZQUFZLENBQUM7SUFDaEMsaUJBQWlCLEVBQUUsTUFBTSxDQUFDLG9CQUFvQixDQUFDO0lBQy9DLG9CQUFvQixFQUFFLE1BQU0sQ0FBQyw4QkFBOEIsQ0FBQztJQUM1RCxjQUFjLEVBQUUsTUFBTSxDQUFDLGdCQUFnQixDQUFDO0lBQ3hDLHNCQUFzQixFQUFFLE1BQU0sQ0FBQyx5QkFBeUIsQ0FBQztJQUN6RCxzQkFBc0IsRUFBRSxNQUFNLENBQUMseUJBQXlCLENBQUM7SUFDekQsWUFBWSxFQUFFLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQztDQUN4QyxDQUFDIn0=