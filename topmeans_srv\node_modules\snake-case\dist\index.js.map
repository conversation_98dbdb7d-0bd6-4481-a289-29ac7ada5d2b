{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,qCAA4C;AAI5C,SAAgB,SAAS,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC5D,OAAO,kBAAO,CAAC,KAAK,qBAClB,SAAS,EAAE,GAAG,IACX,OAAO,EACV,CAAC;AACL,CAAC;AALD,8BAKC", "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function snakeCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"_\",\n    ...options,\n  });\n}\n"]}