{"name": "alipay-sdk", "version": "4.14.0", "engines": {"node": ">=18.0.0"}, "publishConfig": {"tag": "latest", "access": "public"}, "description": "支付宝开放平台 Node.js SDK / Alipay OpenAPI SDK for Node.js", "scripts": {"lint": "eslint src test --ext ts", "test": "npm run lint -- --fix && egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov && npm run prepublishOnly", "prepublishOnly": "tshy && tshy-after"}, "author": "fengmk2", "homepage": "https://github.com/alipay/alipay-sdk-nodejs-all", "bugs": "https://github.com/alipay/alipay-sdk-nodejs-all/issues", "dependencies": {"@fidm/x509": "^1.2.1", "bignumber.js": "^9.1.2", "camelcase-keys": "^7.0.2", "crypto-js": "^4.2.0", "formstream": "^1.5.0", "snakecase-keys": "^8.0.0", "sse-decoder": "^1.0.0", "urllib": "^4", "utility": "^2.1.0"}, "devDependencies": {"@eggjs/tsconfig": "^1.3.3", "@types/crypto-js": "^4.2.2", "@types/mocha": "^10.0.6", "@types/node": "20", "egg-bin": "^6.9.0", "eslint": "8", "eslint-config-egg": "13", "mm": "^3.4.0", "tshy": "^1.14.0", "tshy-after": "^1.0.0", "typescript": "5"}, "repository": "**************:alipay/alipay-sdk-nodejs-all.git", "license": "MIT", "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist", "src"], "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js"}