2025-07-27 21:54:56 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 21:55:22 [info]: MySQL新连接建立
2025-07-27 21:55:22 [info]: 连接从池中获取
2025-07-27 21:55:22 [info]: 登录成功:
2025-07-27 21:55:22 [info]: 连接放回池中
2025-07-27 21:55:23 [info]: 连接从池中获取
2025-07-27 21:55:23 [info]: 连接放回池中
2025-07-27 21:55:23 [info]: 连接从池中获取
2025-07-27 21:55:23 [info]: 连接放回池中
2025-07-27 21:55:24 [info]: 连接从池中获取
2025-07-27 21:55:24 [info]: 连接放回池中
2025-07-27 22:27:54 [info]: 支付宝SDK初始化成功
2025-07-27 22:27:54 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 22:29:28 [info]: 订单创建成功: TM17536265683818048
2025-07-27 22:29:28 [info]: 支付日志记录成功: TM17536265683818048 - create
2025-07-27 22:29:28 [info]: 订单状态更新成功: TM17536265683818048 -> pending
2025-07-27 22:29:28 [info]: 支付日志记录成功: TM17536265683818048 - create
2025-07-27 22:29:28 [info]: 支付订单创建成功: TM17536265683818048
2025-07-27 22:29:32 [info]: 支付日志记录成功: TM17536265683818048 - query
2025-07-27 22:29:32 [error]: 支付宝查询失败:
2025-07-27 22:29:33 [info]: 支付日志记录成功: TM17536265683818048 - close
2025-07-27 22:29:33 [info]: 订单状态更新成功: TM17536265683818048 -> closed
2025-07-27 22:29:33 [info]: 订单关闭成功: TM17536265683818048
2025-07-27 22:29:47 [info]: MySQL新连接建立
2025-07-27 22:29:47 [info]: 连接从池中获取
2025-07-27 22:29:48 [info]: 连接放回池中
2025-07-27 22:31:44 [info]: 支付宝SDK初始化成功
2025-07-27 22:31:44 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 22:32:41 [info]: 订单创建成功: TM17536267618889088
2025-07-27 22:32:41 [info]: 支付日志记录成功: TM17536267618889088 - create
2025-07-27 22:32:42 [error]: 创建支付订单失败: 支付宝接口调用失败: Business Failed
2025-07-27 22:32:42 [info]: 支付日志记录成功: TM17536267618889088 - create
2025-07-27 22:35:12 [info]: 支付宝SDK初始化成功
2025-07-27 22:35:12 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 22:36:03 [info]: 订单创建成功: TM17536269635142204
2025-07-27 22:36:03 [info]: 支付日志记录成功: TM17536269635142204 - create
2025-07-27 22:36:03 [info]: 订单状态更新成功: TM17536269635142204 -> pending
2025-07-27 22:36:03 [info]: 支付日志记录成功: TM17536269635142204 - create
2025-07-27 22:36:03 [info]: 支付订单创建成功(开发模式): TM17536269635142204
2025-07-27 22:37:51 [info]: 订单创建成功: TM17536270709910882
2025-07-27 22:37:51 [info]: 支付日志记录成功: TM17536270709910882 - create
2025-07-27 22:37:51 [info]: 订单状态更新成功: TM17536270709910882 -> pending
2025-07-27 22:37:51 [info]: 支付日志记录成功: TM17536270709910882 - create
2025-07-27 22:37:51 [info]: 支付订单创建成功(开发模式): TM17536270709910882
2025-07-27 22:39:59 [info]: 订单创建成功: TM17536271999765535
2025-07-27 22:40:00 [info]: 支付日志记录成功: TM17536271999765535 - create
2025-07-27 22:40:00 [info]: 订单状态更新成功: TM17536271999765535 -> pending
2025-07-27 22:40:00 [info]: 支付日志记录成功: TM17536271999765535 - create
2025-07-27 22:40:00 [info]: 支付订单创建成功(开发模式): TM17536271999765535
2025-07-27 22:40:09 [error]: 关闭订单失败: HttpClient Request error: Request timeout for 5000 ms
2025-07-27 22:40:09 [info]: 支付日志记录成功: TM17536271999765535 - close
2025-07-27 22:41:03 [info]: 支付宝SDK初始化成功
2025-07-27 22:41:03 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 22:41:12 [info]: MySQL新连接建立
2025-07-27 22:41:12 [info]: 连接从池中获取
2025-07-27 22:41:12 [info]: 登录成功:
2025-07-27 22:41:12 [info]: 连接放回池中
2025-07-27 22:41:12 [info]: 连接从池中获取
2025-07-27 22:41:12 [info]: 连接放回池中
2025-07-27 22:41:12 [info]: 连接从池中获取
2025-07-27 22:41:12 [info]: 连接放回池中
2025-07-27 22:41:13 [info]: 连接从池中获取
2025-07-27 22:41:13 [info]: 连接放回池中
2025-07-27 22:41:15 [info]: 订单创建成功: TM17536272757242334
2025-07-27 22:41:15 [info]: 支付日志记录成功: TM17536272757242334 - create
2025-07-27 22:41:15 [info]: 订单状态更新成功: TM17536272757242334 -> pending
2025-07-27 22:41:15 [info]: 支付日志记录成功: TM17536272757242334 - create
2025-07-27 22:41:15 [info]: 支付订单创建成功(开发模式): TM17536272757242334
2025-07-27 22:41:23 [error]: 关闭订单失败: HttpClient Request error: Request timeout for 5000 ms
2025-07-27 22:41:24 [info]: 支付日志记录成功: TM17536272757242334 - close
2025-07-27 22:57:15 [info]: 支付宝SDK初始化成功
2025-07-27 22:57:15 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 22:57:47 [info]: 订单创建成功: TM17536282673896264
2025-07-27 22:57:47 [info]: 支付日志记录成功: TM17536282673896264 - create
2025-07-27 22:57:47 [error]: 创建支付订单失败: error:1E08010C:DECODER routines::unsupported
2025-07-27 22:57:47 [info]: 支付日志记录成功: TM17536282673896264 - create
2025-07-27 23:12:05 [info]: 支付宝SDK初始化成功
2025-07-27 23:12:05 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 23:12:36 [info]: 订单创建成功: TM17536291564861503
2025-07-27 23:12:36 [info]: 支付日志记录成功: TM17536291564861503 - create
2025-07-27 23:12:36 [info]: 支付宝预创建接口返回:
2025-07-27 23:12:36 [error]: 创建支付订单失败: 支付宝接口调用失败: Business Failed, code: 40004
2025-07-27 23:12:36 [info]: 支付日志记录成功: TM17536291564861503 - create
2025-07-27 23:14:25 [info]: 支付宝SDK初始化成功
2025-07-27 23:14:25 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 23:15:01 [info]: 订单创建成功: TM17536293019054264
2025-07-27 23:15:02 [info]: 支付日志记录成功: TM17536293019054264 - create
2025-07-27 23:15:02 [info]: 支付宝预创建接口请求参数:
2025-07-27 23:15:02 [info]: 支付宝预创建接口返回:
2025-07-27 23:15:02 [error]: 创建支付订单失败: 支付宝接口调用失败: Business Failed, code: 40004
2025-07-27 23:15:02 [info]: 支付日志记录成功: TM17536293019054264 - create
2025-07-27 23:19:11 [info]: 支付宝SDK初始化成功
2025-07-27 23:19:11 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 23:21:08 [info]: 订单创建成功: TM17536296681414585
2025-07-27 23:21:08 [info]: 支付日志记录成功: TM17536296681414585 - create
2025-07-27 23:21:08 [info]: 支付宝预创建接口请求参数:
2025-07-27 23:21:08 [error]: 创建支付订单失败: error:1E08010C:DECODER routines::unsupported
2025-07-27 23:21:08 [info]: 支付日志记录成功: TM17536296681414585 - create
2025-07-27 23:28:28 [info]: 支付宝SDK初始化成功
2025-07-27 23:28:28 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 23:28:59 [info]: 订单创建成功: TM17536301396011667
2025-07-27 23:28:59 [info]: 支付日志记录成功: TM17536301396011667 - create
2025-07-27 23:28:59 [info]: 支付宝预创建接口请求参数:
2025-07-27 23:29:05 [error]: 创建支付订单失败: HttpClient Request error: Request timeout for 5000 ms
2025-07-27 23:29:05 [info]: 支付日志记录成功: TM17536301396011667 - create
2025-07-27 23:37:36 [info]: 支付宝SDK初始化成功
2025-07-27 23:37:36 [info]: topmeans服务已启动，监听 3999 端口中...
2025-07-27 23:37:45 [info]: MySQL新连接建立
2025-07-27 23:37:45 [info]: 连接从池中获取
2025-07-27 23:37:46 [info]: 登录成功:
2025-07-27 23:37:46 [info]: 连接放回池中
2025-07-27 23:37:46 [info]: 连接从池中获取
2025-07-27 23:37:46 [info]: 连接放回池中
2025-07-27 23:37:46 [info]: 连接从池中获取
2025-07-27 23:37:46 [info]: 连接放回池中
2025-07-27 23:37:47 [info]: 连接从池中获取
2025-07-27 23:37:47 [info]: 连接放回池中
2025-07-27 23:37:50 [info]: 订单创建成功: TM17536306700093633
2025-07-27 23:37:50 [info]: 支付日志记录成功: TM17536306700093633 - create
2025-07-27 23:37:50 [info]: 支付宝预创建接口请求参数:
2025-07-27 23:37:55 [error]: 创建支付订单失败: HttpClient Request error: Request timeout for 5000 ms
2025-07-27 23:37:55 [info]: 支付日志记录成功: TM17536306700093633 - create
