import fs from 'node:fs';
import { createHash } from 'node:crypto';
import { BigNumber } from 'bignumber.js';
import { Certificate } from '@fidm/x509';
/** 从公钥证书文件里读取支付宝公钥 */
export function loadPublicKeyFromPath(filePath) {
    const fileData = fs.readFileSync(filePath);
    const certificate = Certificate.fromPEM(fileData);
    return certificate.publicKeyRaw.toString('base64');
}
/** 从公钥证书内容或 Buffer 读取支付宝公钥 */
export function loadPublicKey(content) {
    const pemContent = typeof content === 'string' ? Buffer.from(content) : content;
    const certificate = Certificate.fromPEM(pemContent);
    return certificate.publicKeyRaw.toString('base64');
}
/** 从证书文件里读取序列号 */
export function getSNFromPath(filePath, isRoot = false) {
    const fileData = fs.readFileSync(filePath);
    return getSN(fileData, isRoot);
}
/** 从上传的证书内容或 Buffer 读取序列号 */
export function getSN(fileData, isRoot = false) {
    const pemData = typeof fileData === 'string' ? Buffer.from(fileData) : fileData;
    if (isRoot) {
        return getRootCertSN(pemData);
    }
    const certificate = Certificate.fromPEM(pemData);
    return getCertSN(certificate);
}
/** 读取序列号 */
function getCertSN(certificate) {
    const { issuer, serialNumber } = certificate;
    const principalName = issuer.attributes
        .reduceRight((prev, curr) => {
        const { shortName, value } = curr;
        const result = `${prev}${shortName}=${value},`;
        return result;
    }, '')
        .slice(0, -1);
    const decimalNumber = new BigNumber(serialNumber, 16).toString(10);
    const SN = createHash('md5')
        .update(principalName + decimalNumber, 'utf8')
        .digest('hex');
    return SN;
}
/** 读取根证书序列号 */
function getRootCertSN(rootContent) {
    const certificates = Certificate.fromPEMs(rootContent);
    let rootCertSN = '';
    certificates.forEach(item => {
        if (item.signatureOID.startsWith('1.2.840.113549.1.1')) {
            const SN = getCertSN(item);
            if (rootCertSN.length === 0) {
                rootCertSN += SN;
            }
            else {
                rootCertSN += `_${SN}`;
            }
        }
    });
    return rootCertSN;
}
//# sourceMappingURL=data:application/json;base64,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