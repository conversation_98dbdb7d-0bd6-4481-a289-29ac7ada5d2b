# No Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a lower cased string with spaces between words.

## Installation

```
npm install no-case --save
```

## Usage

```js
import { noCase } from "no-case";

noCase("string"); //=> "string"
noCase("dot.case"); //=> "dot case"
noCase("PascalCase"); //=> "pascal case"
noCase("version 1.2.10"); //=> "version 1 2 10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/no-case.svg?style=flat
[npm-url]: https://npmjs.org/package/no-case
[downloads-image]: https://img.shields.io/npm/dm/no-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/no-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/no-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=no-case
