const mysql = require('mysql2/promise');
const logger = require('../log/logger');

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4'
};

/**
 * 订单管理服务
 */
class OrderService {
    /**
     * 生成订单号
     */
    generateOrderId() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `TM${timestamp}${random}`;
    }

    /**
     * 生成商户订单号
     */
    generateOutTradeNo() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `OUT${timestamp}${random}`;
    }

    /**
     * 创建订单
     */
    async createOrder(orderData) {
        const connection = await mysql.createConnection(dbConfig);
        try {
            const orderId = this.generateOrderId();
            const outTradeNo = this.generateOutTradeNo();
            
            // 设置订单过期时间（15分钟后）
            const expireTime = new Date(Date.now() + 15 * 60 * 1000);

            const insertSql = `
                INSERT INTO payment_orders (
                    order_id, out_trade_no, user_id, product_type, product_id, 
                    product_name, total_amount, payment_method, expire_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await connection.execute(insertSql, [
                orderId,
                outTradeNo,
                orderData.userId || null,
                orderData.productType,
                orderData.productId || null,
                orderData.productName,
                orderData.totalAmount,
                orderData.paymentMethod || 'alipay',
                expireTime
            ]);

            logger.info(`订单创建成功: ${orderId}`);
            
            return {
                orderId,
                outTradeNo,
                expireTime
            };
        } catch (error) {
            logger.error('创建订单失败:', error);
            throw error;
        } finally {
            await connection.end();
        }
    }

    /**
     * 更新订单状态
     */
    async updateOrderStatus(orderId, status, updateData = {}) {
        const connection = await mysql.createConnection(dbConfig);
        try {
            let updateSql = 'UPDATE payment_orders SET payment_status = ?';
            const params = [status];

            if (updateData.tradeNo) {
                updateSql += ', trade_no = ?';
                params.push(updateData.tradeNo);
            }

            if (updateData.qrCodeUrl) {
                updateSql += ', qr_code_url = ?';
                params.push(updateData.qrCodeUrl);
            }

            if (status === 'paid') {
                updateSql += ', paid_time = NOW()';
            }

            updateSql += ' WHERE order_id = ?';
            params.push(orderId);

            const [result] = await connection.execute(updateSql, params);
            
            if (result.affectedRows === 0) {
                throw new Error(`订单不存在: ${orderId}`);
            }

            logger.info(`订单状态更新成功: ${orderId} -> ${status}`);
            return true;
        } catch (error) {
            logger.error('更新订单状态失败:', error);
            throw error;
        } finally {
            await connection.end();
        }
    }

    /**
     * 查询订单
     */
    async getOrder(orderId) {
        const connection = await mysql.createConnection(dbConfig);
        try {
            const [rows] = await connection.execute(
                'SELECT * FROM payment_orders WHERE order_id = ?',
                [orderId]
            );

            if (rows.length === 0) {
                return null;
            }

            return rows[0];
        } catch (error) {
            logger.error('查询订单失败:', error);
            throw error;
        } finally {
            await connection.end();
        }
    }

    /**
     * 根据商户订单号查询订单
     */
    async getOrderByOutTradeNo(outTradeNo) {
        const connection = await mysql.createConnection(dbConfig);
        try {
            const [rows] = await connection.execute(
                'SELECT * FROM payment_orders WHERE out_trade_no = ?',
                [outTradeNo]
            );

            if (rows.length === 0) {
                return null;
            }

            return rows[0];
        } catch (error) {
            logger.error('查询订单失败:', error);
            throw error;
        } finally {
            await connection.end();
        }
    }

    /**
     * 记录支付日志
     */
    async logPaymentAction(orderId, actionType, requestData, responseData, status, errorMessage = null) {
        const connection = await mysql.createConnection(dbConfig);
        try {
            const insertSql = `
                INSERT INTO payment_logs (
                    order_id, action_type, request_data, response_data, 
                    status, error_message
                ) VALUES (?, ?, ?, ?, ?, ?)
            `;

            await connection.execute(insertSql, [
                orderId,
                actionType,
                JSON.stringify(requestData),
                JSON.stringify(responseData),
                status,
                errorMessage
            ]);

            logger.info(`支付日志记录成功: ${orderId} - ${actionType}`);
        } catch (error) {
            logger.error('记录支付日志失败:', error);
        } finally {
            await connection.end();
        }
    }

    /**
     * 关闭过期订单
     */
    async closeExpiredOrders() {
        const connection = await mysql.createConnection(dbConfig);
        try {
            const updateSql = `
                UPDATE payment_orders 
                SET payment_status = 'closed' 
                WHERE payment_status = 'pending' 
                AND expire_time < NOW()
            `;

            const [result] = await connection.execute(updateSql);
            
            if (result.affectedRows > 0) {
                logger.info(`关闭过期订单数量: ${result.affectedRows}`);
            }

            return result.affectedRows;
        } catch (error) {
            logger.error('关闭过期订单失败:', error);
            throw error;
        } finally {
            await connection.end();
        }
    }
}

module.exports = new OrderService();
