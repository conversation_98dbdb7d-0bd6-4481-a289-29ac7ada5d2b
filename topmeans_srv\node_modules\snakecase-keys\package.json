{"name": "snakecase-keys", "main": "index.js", "version": "8.1.0", "description": "Convert an object's keys to snake case", "license": "MIT", "repository": "bendrucker/snakecase-keys", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "scripts": {"test": "standard && tape test.js && tsd"}, "keywords": ["snake", "case", "camel", "keys", "object"], "devDependencies": {"standard": "^17.0.0", "tape": "^5.0.1", "tsd": "^0.32.0"}, "files": ["index.js", "index.d.ts"], "dependencies": {"map-obj": "^4.2.0", "snake-case": "^3.0.4", "type-fest": "^4.15.0"}, "engines": {"node": ">=18"}}