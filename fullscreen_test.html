<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏登录页面测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .device-comparison {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .device-frame {
            border: 3px solid #333;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            background: #000;
            margin: 10px;
        }
        .desktop-frame {
            width: 800px;
            height: 600px;
        }
        .mobile-frame {
            width: 375px;
            height: 667px;
        }
        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .device-label {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .critical-test {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        .checklist {
            background: #fff3e0;
            border-left-color: #ff9800;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .refresh-btn {
            background: #4caf50;
        }
        .clear-btn {
            background: #ff9800;
        }
        ul, ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔒 全屏登录页面测试</h1>
        
        <div class="test-info critical-test">
            <h3>🚨 关键测试要求</h3>
            <p><strong>登录页面必须在所有设备上完全全屏显示，不能显示任何其他页面内容！</strong></p>
            <ul>
                <li>❌ <strong>不允许</strong>：看到旅游攻略制作页面内容</li>
                <li>❌ <strong>不允许</strong>：看到上方选项卡内容</li>
                <li>❌ <strong>不允许</strong>：看到任何导航栏</li>
                <li>✅ <strong>只允许</strong>：登录页面的背景、品牌信息、登录表单</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="refreshFrames()" class="refresh-btn">🔄 刷新页面</button>
            <button onclick="clearStorage()" class="clear-btn">🗑️ 清除登录状态</button>
            <button onclick="testFullscreen()">📏 检查全屏效果</button>
        </div>

        <div class="device-comparison">
            <div class="desktop-frame device-frame">
                <div class="device-label">桌面端 (800x600)</div>
                <iframe 
                    id="desktopScreen" 
                    class="device-screen" 
                    src="http://localhost:5173/"
                    title="桌面端登录页面">
                </iframe>
            </div>
            
            <div class="mobile-frame device-frame">
                <div class="device-label">移动端 (375x667)</div>
                <iframe 
                    id="mobileScreen" 
                    class="device-screen" 
                    src="http://localhost:5173/"
                    title="移动端登录页面">
                </iframe>
            </div>
        </div>

        <div class="test-info checklist">
            <h3>📋 全屏效果检查清单</h3>
            <h4>桌面端检查：</h4>
            <ul>
                <li>□ 登录页面完全覆盖整个屏幕</li>
                <li>□ 只能看到渐变背景</li>
                <li>□ 只能看到品牌信息（TopMeansLab + 四个功能图标）</li>
                <li>□ 只能看到登录表单</li>
                <li>□ 不能看到任何其他页面内容</li>
            </ul>
            
            <h4>移动端检查：</h4>
            <ul>
                <li>□ 登录页面完全覆盖整个屏幕</li>
                <li>□ <span class="highlight">不能看到上方的旅游攻略制作页面</span></li>
                <li>□ <span class="highlight">不能看到任何选项卡或导航栏</span></li>
                <li>□ 只能看到登录页面的渐变背景</li>
                <li>□ 四个功能图标横排显示</li>
                <li>□ 页面可以上下滚动查看完整登录表单</li>
                <li>□ 验证码区域在登录框内正确显示</li>
            </ul>
        </div>

        <div class="test-info">
            <h3>🧪 功能测试</h3>
            <p><strong>测试账号：</strong></p>
            <ul>
                <li>账号: testuser</li>
                <li>密码: test123456</li>
                <li>验证码: 不区分大小写</li>
            </ul>
            
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>确认两个设备都显示完全全屏的登录页面</li>
                <li>在移动端测试滚动功能</li>
                <li>测试登录功能是否正常</li>
                <li>登录成功后应该跳转到攻略制作页面</li>
            </ol>
        </div>

        <div class="test-info success">
            <h3>✅ 预期正确效果</h3>
            <p><strong>桌面端：</strong>左右对称布局，完全全屏，精致美观</p>
            <p><strong>移动端：</strong>垂直布局，完全全屏，可滚动，四个图标横排</p>
            <p><strong>共同点：</strong>都只显示登录页面内容，不显示任何其他页面元素</p>
        </div>

        <div class="test-info critical-test">
            <h3>⚠️ 如果看到问题</h3>
            <p>如果在移动端看到了旅游攻略制作页面或其他内容，说明：</p>
            <ul>
                <li>z-index 层级不够高</li>
                <li>position: fixed 没有生效</li>
                <li>容器没有完全覆盖屏幕</li>
                <li>需要进一步调整CSS样式</li>
            </ul>
        </div>
    </div>

    <script>
        function refreshFrames() {
            const desktop = document.getElementById('desktopScreen');
            const mobile = document.getElementById('mobileScreen');
            desktop.src = desktop.src;
            mobile.src = mobile.src;
        }

        function clearStorage() {
            // 清除localStorage来模拟未登录状态
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
            setTimeout(() => {
                refreshFrames();
            }, 500);
        }

        function testFullscreen() {
            alert('请仔细检查两个设备模拟器：\n\n' +
                  '✅ 桌面端：应该只看到登录页面\n' +
                  '✅ 移动端：应该只看到登录页面，可以滚动\n\n' +
                  '❌ 如果看到其他内容，说明全屏效果有问题');
        }

        // 页面加载完成后的检查
        window.onload = function() {
            console.log('全屏登录页面测试已加载');
            
            // 检查服务状态
            Promise.all([
                fetch('http://localhost:5173/').then(r => r.ok),
                fetch('http://localhost:3999/api/health').then(r => r.ok)
            ]).then(([frontend, backend]) => {
                if (frontend && backend) {
                    console.log('✅ 前端和后端服务都正常');
                } else {
                    console.warn('⚠️ 部分服务可能未启动');
                }
            }).catch(error => {
                console.error('❌ 服务检查失败:', error);
            });
            
            // 5秒后提醒用户检查
            setTimeout(() => {
                if (confirm('现在开始检查全屏效果吗？\n\n请确认移动端是否只显示登录页面内容，没有其他页面元素。')) {
                    // 用户确认后可以进行更详细的检查
                }
            }, 5000);
        };
    </script>
</body>
</html>
