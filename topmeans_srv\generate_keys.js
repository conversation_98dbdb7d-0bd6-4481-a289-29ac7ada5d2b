const crypto = require('crypto');
const fs = require('fs');

// 生成RSA密钥对
const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
    },
    privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
    }
});

console.log('生成的私钥:');
console.log(privateKey);
console.log('\n生成的公钥:');
console.log(publicKey);

// 保存到文件
fs.writeFileSync('./src/config/privateKey.txt', privateKey);
fs.writeFileSync('./src/config/publicKey.txt', publicKey);

console.log('\n密钥已保存到文件');

// 输出不带头尾的密钥内容
const privateKeyContent = privateKey.replace(/-----BEGIN PRIVATE KEY-----/g, '')
                                   .replace(/-----END PRIVATE KEY-----/g, '')
                                   .replace(/\s/g, '');

const publicKeyContent = publicKey.replace(/-----BEGIN PUBLIC KEY-----/g, '')
                                  .replace(/-----END PUBLIC KEY-----/g, '')
                                  .replace(/\s/g, '');

console.log('\n应用私钥（去除头尾）:');
console.log(privateKeyContent);

console.log('\n应用公钥（去除头尾）:');
console.log(publicKeyContent);
