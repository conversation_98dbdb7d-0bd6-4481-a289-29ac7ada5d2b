const express = require('express');
const { fetchAMapData } = require('../controllers/apiController');
const { getAMapKeys,
    callExternalDSAPI,
    saveAmapImg,
    savePlan } = require('../services/apiService');

const { dbHealthCheck,
    userRegister,
    userLogin,
    userInfo,
    userProfile,
    userUpdate,
    userUpdatePassword,
    userUploadAvatar,
    uploadAvatar,
    addOnePlan,
    getAllPlans,
    getUserGuideDetails } = require('../services/userApi');

const { hotelInfo, foodPhoto, viewPhoto } = require('../services/wormService');
const { scrapeImageUrl } = require('../services/scraper')
const { genImg } = require('../services/jimengService');
const { fetchAIImg } = require('../services/tywxService');
const alipayService = require('../services/alipayService');

const router = express.Router();

// plan apis
router.get('/amap_keys', getAMapKeys);
router.get('/amap', fetchAMapData);
router.post('/ds', callExternalDSAPI);
router.post('/save_plan', savePlan);
router.post('/save_amap_img', saveAmapImg);
router.post('/hotel', hotelInfo);
router.post('/food', foodPhoto);
router.post('/view', scrapeImageUrl);
router.post('/ai_img', genImg);
router.post('/ai_img2', fetchAIImg);

// user apis
router.get('/health', dbHealthCheck);
router.post('/user/register', userRegister);
router.post('/user/login', userLogin);
router.get('/user/info', userInfo);
router.get('/user/profile', userProfile);
router.put('/user/profile', userUpdate);
router.put('/user/password', (req, res, next) => {
    const { userId, oldPassword, newPassword } = req.body
    if (!userId || !oldPassword || !newPassword) {
        return res.status(400).json({
            success: false,
            message: '缺少必要参数'
        })
    }
    next()
}, userUpdatePassword);

router.put('/user/avatar', (req, res, next) => {
    next();
},
    (req, res, next) => {
        uploadAvatar(req, res, (err) => {
            if (err) {
                // 处理multer错误（如文件大小超限）
                return res.status(400).json({
                    success: false,
                    message: err.message
                });
            }
            next();
        });
    }, userUploadAvatar);

router.post('/user/add_plan', async (req, res) => {
    const result = await addOnePlan(req.body);
    res.json(result);
});

router.get('/user/query_plan', async (req, res) => {
    const result = await getAllPlans(req.body);
    res.json(result);
});

// 获取用户攻略详情
router.get('/user/guides', async (req, res) => {
    try {
        const { account } = req.query;
        if (!account) {
            return res.status(400).json({
                success: false,
                message: '缺少账号参数'
            });
        }
        const result = await getUserGuideDetails({ account });
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 支付相关路由
router.post('/payment/create', async (req, res) => {
    try {
        const { amount, productType, productId, productName, userId } = req.body;

        if (!amount || amount <= 0) {
            return res.status(400).json({ error: '支付金额无效' });
        }

        const result = await alipayService.createPayment({
            amount: parseFloat(amount),
            productType: productType || 'single_service',
            productId,
            productName: productName || '服务购买',
            userId
        });

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        res.status(500).json({
            error: '创建支付订单失败',
            details: error.message
        });
    }
});

router.get('/payment/query/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;

        if (!orderId) {
            return res.status(400).json({ error: '订单ID不能为空' });
        }

        const result = await alipayService.queryPayment(orderId);

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        res.status(500).json({
            error: '查询支付状态失败',
            details: error.message
        });
    }
});

router.post('/payment/close/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;

        if (!orderId) {
            return res.status(400).json({ error: '订单ID不能为空' });
        }

        const result = await alipayService.closeOrder(orderId);

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        res.status(500).json({
            error: '关闭订单失败',
            details: error.message
        });
    }
});

router.post('/payment/notify', async (req, res) => {
    try {
        const notifyData = req.body;
        const result = await alipayService.handleNotify(notifyData);

        if (result) {
            res.send('success');
        } else {
            res.send('fail');
        }
    } catch (error) {
        res.send('fail');
    }
});

module.exports = router;