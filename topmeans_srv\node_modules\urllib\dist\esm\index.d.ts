import { HttpClient } from './HttpClient.js';
import { RequestOptions, RequestURL } from './Request.js';
export declare function getDefaultHttpClient(rejectUnauthorized?: boolean, allowH2?: boolean): HttpClient;
interface UrllibRequestOptions extends RequestOptions {
    /**
     * If `true`, the server certificate is verified against the list of supplied CAs.
     * An 'error' event is emitted if verification fails.
     * Default: `true`
     */
    rejectUnauthorized?: boolean;
    /** Allow to use HTTP2 first. Default is `false` */
    allowH2?: boolean;
}
export declare function request<T = any>(url: RequestURL, options?: UrllibRequestOptions): Promise<import("./Response.js").HttpClientResponse<T>>;
export declare function curl<T = any>(url: RequestURL, options?: UrllibRequestOptions): Promise<import("./Response.js").HttpClientResponse<T>>;
export { MockA<PERSON>, ProxyAgent, Agent, Dispatcher, setGlobalDispatcher, getGlobalDispatcher, Request, RequestInfo, RequestInit, Response, BodyInit, ResponseInit, Headers, FormData, } from 'undici';
export { HttpClient, HttpClient as HttpClient2, HEADER_USER_AGENT as USER_AGENT, RequestDiagnosticsMessage, ResponseDiagnosticsMessage, ClientOptions, } from './HttpClient.js';
export { RequestOptions, RequestOptions as RequestOptions2, RequestURL, HttpMethod, FixJSONCtlCharsHandler, FixJSONCtlChars, } from './Request.js';
export { CheckAddressFunction } from './HttpAgent.js';
export { SocketInfo, Timing, RawResponseWithMeta, HttpClientResponse, } from './Response.js';
export { IncomingHttpHeaders, } from './IncomingHttpHeaders.js';
export * from './HttpClientError.js';
export { FetchFactory, fetch } from './fetch.js';
export { FormData as WebFormData } from './FormData.js';
declare const _default: {
    request: typeof request;
    curl: typeof curl;
    USER_AGENT: string;
};
export default _default;
