const axios = require('axios');

async function testPaymentAPI() {
    try {
        console.log('开始测试支付API...');
        
        // 测试创建支付订单
        const createResponse = await axios.post('http://localhost:3999/api/payment/create', {
            amount: 0.01,
            productType: 'single_service',
            productName: '测试服务',
            userId: null
        });
        
        console.log('创建支付订单响应:', JSON.stringify(createResponse.data, null, 2));
        
        if (createResponse.data.success) {
            const orderId = createResponse.data.data.orderId;
            console.log('订单创建成功，订单ID:', orderId);
            
            // 等待2秒后查询订单状态
            setTimeout(async () => {
                try {
                    const queryResponse = await axios.get(`http://localhost:3999/api/payment/query/${orderId}`);
                    console.log('查询支付状态响应:', JSON.stringify(queryResponse.data, null, 2));
                } catch (error) {
                    console.error('查询支付状态失败:', error.response?.data || error.message);
                }
            }, 2000);
        }
        
    } catch (error) {
        console.error('测试失败:', error.response?.data || error.message);
    }
}

testPaymentAPI();
