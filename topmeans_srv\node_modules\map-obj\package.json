{"name": "map-obj", "version": "4.3.0", "description": "Map object keys and values into a new object", "license": "MIT", "repository": "sindresorhus/map-obj", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["map", "object", "key", "keys", "value", "values", "iterate", "iterator", "rename", "modify", "deep", "recurse", "recursive"], "devDependencies": {"ava": "^2.0.0", "tsd": "^0.14.0", "xo": "^0.24.0"}}