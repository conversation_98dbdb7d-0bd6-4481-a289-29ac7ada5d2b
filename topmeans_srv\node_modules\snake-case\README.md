# Snake Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Transform into a lower case string with underscores between words.

## Installation

```
npm install snake-case --save
```

## Usage

```js
import { snakeCase } from "snake-case";

snakeCase("string"); //=> "string"
snakeCase("dot.case"); //=> "dot_case"
snakeCase("PascalCase"); //=> "pascal_case"
snakeCase("version 1.2.10"); //=> "version_1_2_10"
```

The function also accepts [`options`](https://github.com/blakeembrey/change-case#options).

## License

MIT

[npm-image]: https://img.shields.io/npm/v/snake-case.svg?style=flat
[npm-url]: https://npmjs.org/package/snake-case
[downloads-image]: https://img.shields.io/npm/dm/snake-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/snake-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/snake-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=snake-case
