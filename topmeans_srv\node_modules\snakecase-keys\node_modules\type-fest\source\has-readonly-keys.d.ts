import type {ReadonlyKeysOf} from './readonly-keys-of';

/**
Creates a type that represents `true` or `false` depending on whether the given type has any readonly fields.

This is useful when you want to create an API whose behavior depends on the presence or absence of readonly fields.

@example
```
import type {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>onlyKeysOf} from 'type-fest';

type UpdateService<Entity extends object> = {
	removeField: HasReadonlyKeys<Entity> extends true
		? (field: Readonly<PERSON>eysOf<Entity>) => Promise<void>
		: never
}
```

@category Utilities
*/
export type HasReadonlyKeys<BaseType extends object> = ReadonlyKeysOf<BaseType> extends never ? false : true;
