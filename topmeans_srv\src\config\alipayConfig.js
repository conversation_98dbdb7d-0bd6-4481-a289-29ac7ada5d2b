const { AlipaySdk } = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const logger = require('../log/logger');

/**
 * 支付宝配置服务
 */
class AlipayConfig {
    constructor() {
        this.alipaySdk = null;
        this.init();
    }

    /**
     * 初始化支付宝SDK
     */
    init() {
        try {
            // 读取环境变量
            const appId = process.env.alipayAppID;
            if (!appId) {
                throw new Error('支付宝AppID未配置');
            }

            // 读取私钥文件
            const privateKeyPath = path.join(__dirname, 'privateKey.txt');
            let privateKey = fs.readFileSync(privateKeyPath, 'utf8').trim();
            // 移除私钥的头尾标识，只保留密钥内容
            privateKey = privateKey.replace(/-----BEGIN PRIVATE KEY-----/g, '')
                                   .replace(/-----END PRIVATE KEY-----/g, '')
                                   .replace(/\s/g, '');

            // 读取公钥文件
            const publicKeyPath = path.join(__dirname, 'publicKey.txt');
            let alipayPublicKey = fs.readFileSync(publicKeyPath, 'utf8').trim();
            // 移除公钥的头尾标识，只保留密钥内容
            alipayPublicKey = alipayPublicKey.replace(/-----BEGIN PUBLIC KEY-----/g, '')
                                             .replace(/-----END PUBLIC KEY-----/g, '')
                                             .replace(/\s/g, '');

            // 初始化支付宝SDK
            this.alipaySdk = new AlipaySdk({
                appId: appId,
                privateKey: privateKey,
                alipayPublicKey: alipayPublicKey,
                // gateway: 'https://openapi.alipay.com/gateway.do', // 正式环境
                gateway: 'https://openapi.alipaydev.com/gateway.do', // 沙箱环境
            });

            logger.info('支付宝SDK初始化成功');
        } catch (error) {
            logger.error('支付宝SDK初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取支付宝SDK实例
     */
    getSdk() {
        if (!this.alipaySdk) {
            throw new Error('支付宝SDK未初始化');
        }
        return this.alipaySdk;
    }
}

// 创建单例实例
const alipayConfig = new AlipayConfig();

module.exports = alipayConfig;
