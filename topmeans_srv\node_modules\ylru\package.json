{"name": "ylru", "description": "Extends LRU base on hashlru", "version": "2.0.0", "engines": {"node": ">= 18.19.0"}, "homepage": "https://github.com/node-modules/ylru", "repository": {"type": "git", "url": "git://github.com/node-modules/ylru.git"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@eggjs/tsconfig": "1", "@types/node": "20", "@types/mocha": "10", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.3", "egg-bin": "6", "eslint": "8", "eslint-config-egg": "13", "git-contributor": "2", "hashlru": "^1.0.3", "lru-cache": "^4.0.2", "runscript": "^1.5.2", "typescript": "5", "tshy": "1", "tshy-after": "1"}, "scripts": {"contributor": "git-contributor", "bench": "npm run prepublishOnly && node test/bench.cjs", "lint": "eslint src test --ext .ts", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov && npm run prepublishOnly && attw --pack && npm run bench", "prepublishOnly": "tshy && tshy-after"}, "author": "fengmk2", "license": "MIT", "type": "module", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "exports": {".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "types": "./dist/commonjs/index.d.ts", "main": "./dist/commonjs/index.js"}