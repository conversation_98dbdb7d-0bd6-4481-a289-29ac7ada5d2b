-- 支付订单表
CREATE TABLE IF NOT EXISTS payment_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL UNIQUE COMMENT '订单号（系统生成）',
    out_trade_no VARCHAR(64) NOT NULL UNIQUE COMMENT '商户订单号',
    trade_no VARCHAR(64) DEFAULT NULL COMMENT '支付宝交易号',
    user_id INT DEFAULT NULL COMMENT '用户ID',
    product_type ENUM('single_service', 'vip_plan', 'recharge') NOT NULL COMMENT '产品类型',
    product_id VARCHAR(32) DEFAULT NULL COMMENT '产品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '产品名称',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    payment_method ENUM('alipay', 'wechat') NOT NULL DEFAULT 'alipay' COMMENT '支付方式',
    payment_status ENUM('pending', 'paid', 'failed', 'cancelled', 'closed') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
    qr_code_url TEXT DEFAULT NULL COMMENT '支付二维码URL',
    notify_url VARCHAR(255) DEFAULT NULL COMMENT '异步通知地址',
    return_url VARCHAR(255) DEFAULT NULL COMMENT '同步跳转地址',
    expire_time DATETIME DEFAULT NULL COMMENT '订单过期时间',
    paid_time DATETIME DEFAULT NULL COMMENT '支付完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_id (order_id),
    INDEX idx_out_trade_no (out_trade_no),
    INDEX idx_trade_no (trade_no),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单表';

-- 支付日志表
CREATE TABLE IF NOT EXISTS payment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(64) NOT NULL COMMENT '订单号',
    action_type ENUM('create', 'query', 'notify', 'close', 'refund') NOT NULL COMMENT '操作类型',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    status ENUM('success', 'failed') NOT NULL COMMENT '操作状态',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_order_id (order_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付操作日志表';
