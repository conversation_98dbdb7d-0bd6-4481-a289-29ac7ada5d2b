<template>
  <div class="payment-methods">
    <el-card class="payment-card">
      <template #header>
        <div class="card-header">
          <span>选择支付方式</span>
        </div>
      </template>

      <el-radio-group v-model="selectedMethod" class="payment-methods-group">
        <el-radio-button label="alipay">
          <img :src="'/images/alipay.png?url'" alt="支付宝" class="payment-icon" />
          <span>支付宝</span>
        </el-radio-button>
      </el-radio-group>

      <div class="payment-amount">
        <span class="amount-label">支付金额：</span>
        <span class="amount-value">¥{{ amount }}</span>
      </div>

      <div class="payment-actions">
        <el-button type="primary" @click="handlePayment" :loading="loading">
          确认支付
        </el-button>
      </div>
    </el-card>

    <!-- 支付二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="扫码支付"
      width="300px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="qr-code-container">
        <div v-if="qrCodeUrl.includes('mock_')" class="mock-qr-code">
          <div class="mock-qr-placeholder">
            <div class="qr-pattern"></div>
            <p>模拟二维码</p>
            <p class="mock-tip">开发环境下，30秒后自动模拟支付成功</p>
          </div>
        </div>
        <img v-else :src="qrCodeUrl" alt="支付二维码" class="qr-code" />
        <p class="qr-code-tip">请使用{{ selectedMethod === 'alipay' ? '支付宝' : '微信' }}扫码支付</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelPayment">取消支付</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  amount: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['payment-success', 'payment-cancel'])

const selectedMethod = ref('alipay')
const loading = ref(false)
const qrCodeVisible = ref(false)
const qrCodeUrl = ref('')
const currentOrderId = ref('')
const pollingTimer = ref(null)

const handlePayment = async () => {
  loading.value = true
  try {
    // 调用后端接口创建支付订单
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: props.amount,
        productType: 'single_service',
        productName: '服务购买',
        userId: null // 可以从用户状态中获取
      })
    })

    if (!response.ok) {
      throw new Error('创建支付订单失败')
    }

    const result = await response.json()

    if (result.success) {
      currentOrderId.value = result.data.orderId
      qrCodeUrl.value = result.data.qrCodeUrl
      qrCodeVisible.value = true

      // 开始轮询支付状态
      startPaymentPolling()
    } else {
      throw new Error(result.error || '创建支付订单失败')
    }
  } catch (error) {
    ElMessage.error(error.message || '获取支付二维码失败')
  } finally {
    loading.value = false
  }
}

const cancelPayment = () => {
  // 停止轮询
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }

  // 关闭订单
  if (currentOrderId.value) {
    closeOrder()
  }

  qrCodeVisible.value = false
  currentOrderId.value = ''
  emit('payment-cancel')
}

// 开始支付状态轮询
const startPaymentPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  pollingTimer.value = setInterval(async () => {
    await checkPaymentStatus()
  }, 3000) // 每3秒查询一次
}

// 检查支付状态
const checkPaymentStatus = async () => {
  if (!currentOrderId.value) return

  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/query/${currentOrderId.value}`)

    if (!response.ok) {
      throw new Error('查询支付状态失败')
    }

    const result = await response.json()

    if (result.success) {
      const { status } = result.data

      if (status === 'paid') {
        // 支付成功
        if (pollingTimer.value) {
          clearInterval(pollingTimer.value)
          pollingTimer.value = null
        }

        qrCodeVisible.value = false
        currentOrderId.value = ''
        ElMessage.success('支付成功！')
        emit('payment-success')
      } else if (status === 'closed' || status === 'cancelled') {
        // 订单已关闭或取消
        if (pollingTimer.value) {
          clearInterval(pollingTimer.value)
          pollingTimer.value = null
        }

        qrCodeVisible.value = false
        currentOrderId.value = ''
        ElMessage.warning('订单已关闭')
        emit('payment-cancel')
      }
    }
  } catch (error) {
    console.error('查询支付状态失败:', error)
  }
}

// 关闭订单
const closeOrder = async () => {
  if (!currentOrderId.value) return

  try {
    await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/close/${currentOrderId.value}`, {
      method: 'POST'
    })
  } catch (error) {
    console.error('关闭订单失败:', error)
  }
}

// 组件销毁时清理定时器
onUnmounted(() => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
})
</script>

<style scoped>
.payment-methods {
  padding: 20px;
}

.payment-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.payment-methods-group {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.payment-amount {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
}

.amount-label {
  color: #666;
}

.amount-value {
  color: #f56c6c;
  font-weight: bold;
  font-size: 24px;
}

.payment-actions {
  text-align: center;
  margin-top: 20px;
}

.qr-code-container {
  text-align: center;
  padding: 20px 0;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}

.qr-code-tip {
  color: #666;
  font-size: 14px;
}
</style> 